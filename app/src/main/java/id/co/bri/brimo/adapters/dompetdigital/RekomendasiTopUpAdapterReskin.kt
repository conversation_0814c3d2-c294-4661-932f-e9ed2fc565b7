package id.co.bri.brimo.adapters.dompetdigital

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemNominalReskinBinding
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.OptionAmountItem
class RekomendasiTopUpAdapterReskin(
    private val onItemClick: (OptionAmountItem) -> Unit,
    private val items: List<OptionAmountItem>,
) : RecyclerView.Adapter<RekomendasiTopUpAdapterReskin.ViewHolder>() {

    private var selectedPosition = RecyclerView.NO_POSITION

    inner class ViewHolder(val binding: ItemNominalReskinBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setOnClickListener {
                val previousPosition = selectedPosition
                selectedPosition = adapterPosition
                notifyItemChanged(previousPosition)
                notifyItemChanged(selectedPosition)
                onItemClick(items[adapterPosition])
            }
        }

        fun bind(nominal: String, isSelected: Boolean) {
            binding.textNominal.text = nominal
            binding.root.setBackgroundResource(
                if (isSelected) R.drawable.bg_selected_reskin else R.drawable.bg_unselected_reskin
            )
            binding.textNominal.setTextColor(
                ContextCompat.getColor(binding.root.context,
                    if (isSelected) R.color.white else R.color.black
                )
            )
        }
    }

    fun setSelectedPosition(position: Int) {
        val previousPosition = selectedPosition
        selectedPosition = position
        if (previousPosition != RecyclerView.NO_POSITION) notifyItemChanged(previousPosition)
        if (selectedPosition != RecyclerView.NO_POSITION) notifyItemChanged(selectedPosition)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemNominalReskinBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val isSelected = position == selectedPosition
        holder.bind(items[position].name, isSelected)
    }
}