package id.co.bri.brimo.domain.helpers.textwatcher;

import android.text.Editable;
import android.text.Selection;
import android.text.TextWatcher;
import android.widget.EditText;

import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;

public class AmountFormatWatcherReskin implements TextWatcher {
    EditText editText;
    onAmountChange amountListener = null;
    boolean withCurrency;

    public AmountFormatWatcherReskin(EditText et, onAmountChange listener) {
        this.editText = et;
        this.amountListener = listener;
        this.withCurrency = true;
    }

    public AmountFormatWatcherReskin(EditText et, onAmountChange listener, boolean withCurrency) {
        this.editText = et;
        this.amountListener = listener;
        this.withCurrency = withCurrency;
    }

    public AmountFormatWatcherReskin(EditText et) {
        editText = et;
    }

    @Override
    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void afterTextChanged(Editable editable) {
        try {
            editText.removeTextChangedListener(this);

            String value = editText.getText().toString();
            int currentCursorPosition = editText.getSelectionStart();

            if (withCurrency) {
                if (!value.startsWith(Constant.CURRENCY)) {
                    // If text doesn't start with "Rp", prepend it to the existing text
                    String str = value.replaceAll(Constant.CURRENCY, "").replaceAll("\\.", "");
                    if (!str.isEmpty()) {
                        str = getDecimalFormattedString(str);
                        value = Constant.CURRENCY + str;
                        editText.setText(value);
                        // Position cursor after "Rp" prefix if it was before
                        int newCursorPosition = Math.max(2, currentCursorPosition + 2);
                        Selection.setSelection(editText.getText(), Math.min(newCursorPosition, value.length()));

                        if (amountListener != null) {
                            amountListener.onAmountChange(getAmountNumber(str));
                        }
                    } else {
                        // Only set to "Rp" if the input is completely empty
                        editText.setText(Constant.CURRENCY);
                        Selection.setSelection(editText.getText(), 2);
                    }
                } else {
                    String str = value.replaceAll(Constant.CURRENCY, "").replaceAll("\\.", "");
                    if (!str.isEmpty()) {
                        str = getDecimalFormattedString(str);
                        value = Constant.CURRENCY + str;
                        editText.setText(value);
                        // Maintain cursor position, but ensure it's after "Rp"
                        int newCursorPosition = Math.max(2, currentCursorPosition);
                        Selection.setSelection(editText.getText(), Math.min(newCursorPosition, value.length()));

                        if (amountListener != null) {
                            amountListener.onAmountChange(getAmountNumber(str));
                        }
                    } else {
                        editText.setText(Constant.CURRENCY);
                        Selection.setSelection(editText.getText(), 2);
                    }
                }
            } else {
                String str = value.replaceAll(Constant.CURRENCY, "").replaceAll("\\.", "");
                str = getDecimalFormattedString(str);
                value = str;
                editText.setText(value);
                Selection.setSelection(editText.getText(), value.length());

                if (amountListener != null) {
                    amountListener.onAmountChange(getAmountNumber(str));
                }
            }

            editText.addTextChangedListener(this);
        } catch (Exception ex) {
            editText.addTextChangedListener(this);
        }
    }

    private static String getDecimalFormattedString(String value) {
        String val = GeneralHelper.formatNominal(value);
        return val;
    }

    // Method untuk mengembalikan ke numeric
    public static String getAmountNumber(String string) {
        if (!string.equals(""))
            return string.replaceAll("\\.", "").replaceAll("Rp", "");
        else
            return string;
    }

    //interface
    public interface onAmountChange {
        void onAmountChange(String amount);

        void setAmountListener();
    }
}