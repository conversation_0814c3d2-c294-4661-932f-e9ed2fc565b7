package id.co.bri.brimo.domain.helpers.textwatcher;

import android.text.Editable;
import android.text.Selection;
import android.text.TextWatcher;
import android.widget.EditText;

import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;

public class AmountFormatWatcherReskin implements TextWatcher {
    EditText editText;
    onAmountChange amountListener = null;
    boolean withCurrency;

    public AmountFormatWatcherReskin(EditText et, onAmountChange listener) {
        this.editText = et;
        this.amountListener = listener;
        this.withCurrency = true;
    }

    public AmountFormatWatcherReskin(EditText et, onAmountChange listener, boolean withCurrency) {
        this.editText = et;
        this.amountListener = listener;
        this.withCurrency = withCurrency;
    }

    public AmountFormatWatcherReskin(EditText et) {
        editText = et;
    }

    @Override
    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void afterTextChanged(Editable editable) {
        try {

            editText.removeTextChangedListener(this);

            String value = editText.getText().toString();

            if (withCurrency) {
                if (!value.startsWith(Constant.CURRENCY)) {
                    // If text doesn't start with "Rp", prepend it to the existing text
                    // This preserves user input instead of replacing it
                    String str = value.replaceAll(Constant.CURRENCY, "").replaceAll("\\.", "");
                    if (!str.isEmpty()) {
                        str = getDecimalFormattedString(str);
                        value = Constant.CURRENCY + str;
                        editText.setText(value);
                        Selection.setSelection(editText.getText(), value.length());

                        if (amountListener != null) {
                            amountListener.onAmountChange(getAmountNumber(str));
                        }
                    } else {
                        // Only set to "Rp" if the input is completely empty
                        editText.setText(Constant.CURRENCY);
                        // Position cursor after "Rp" prefix for user input
                        // Use post to ensure this runs after setText is complete
                        editText.post(new Runnable() {
                            @Override
                            public void run() {
                                Selection.setSelection(editText.getText(), Constant.CURRENCY.length());
                            }
                        });
                    }
                } else {
                    String str = value.replaceAll(Constant.CURRENCY, "").replaceAll("\\.", "");
                    if (str.isEmpty()) {
                        // If only "Rp" with no content, position cursor after "Rp"
                        editText.setText(Constant.CURRENCY);
                        editText.post(new Runnable() {
                            @Override
                            public void run() {
                                Selection.setSelection(editText.getText(), Constant.CURRENCY.length());
                            }
                        });
                    } else {
                        str = getDecimalFormattedString(str);
                        value = Constant.CURRENCY + str;
                        editText.setText(value);
                        Selection.setSelection(editText.getText(), value.length());

                        if (amountListener != null) {
                            amountListener.onAmountChange(getAmountNumber(str));
                        }
                    }
                }
            } else {
                String str = value.replaceAll(Constant.CURRENCY, "").replaceAll("\\.", "");
                str = getDecimalFormattedString(str);
                value = str;
                editText.setText(value);
                Selection.setSelection(editText.getText(), value.length());

                if (amountListener != null) {
                    amountListener.onAmountChange(getAmountNumber(str));
                }
            }

            editText.addTextChangedListener(this);
            return;
        } catch (Exception ex) {
            editText.addTextChangedListener(this);
        }

    }

    private static String getDecimalFormattedString(String value) {
        String val = GeneralHelper.formatNominal(value);
        return val;
    }

    // Method untuk mengembalikan ke numeric
    public static String getAmountNumber(String string) {
        if (!string.equals(""))
            return string.replaceAll("\\.", "").replaceAll("Rp", "");
        else
            return string;
    }

    //interface
    public interface onAmountChange {
        void onAmountChange(String amount);

        void setAmountListener();
    }
}